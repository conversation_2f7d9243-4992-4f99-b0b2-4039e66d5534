<template>
  <v-app id="inspire">
    <!-- Navigation -->
    <v-navigation-drawer v-model="drawer" :width="260" :elevation="10">
      <!-- Logo -->
      <v-sheet
        class="pa-4"
        color="primary"
        d-flex
        align-center
        justify-center
        flex-wrap
        text-center
        mx-auto
        pa-4
      >
        <v-avatar
          class="mb-4"
          color="white"
          size="64"
          image="/image/am.jpg"
        ></v-avatar>
        Systems Platform
      </v-sheet>

      <!-- Account -->
      <v-list>
        <v-list-item
          prepend-avatar="https://cdn.vuetifyjs.com/images/john.png"
          subtitle="Docter"
          title="Mr. <PERSON><PERSON><PERSON>"
        >
        </v-list-item>
      </v-list>

      <!-- Line -->
      <v-divider></v-divider>

      <!-- Menu -->
      <v-list v-model:opened="open" rounded="shaped" color="primary">
        <!-- 1. Dashboard -->
        <v-list-item
          v-for="([title, icon, link], i) in dash"
          :key="i"
          :prepend-icon="icon"
          :title="title"
          :value="title"
          @click="navigateTo(link)"
          rounded="shaped"
          color="primary"
        ></v-list-item>
        <!-- 2. Account -->
        <v-list-group value="Account" rounded="shaped" color="primary">
          <template v-slot:activator="{ props }">
            <v-list-item
              v-bind="props"
              prepend-icon="mdi-account-cog"
              title="Account"
              rounded="shaped"
              color="primary"
            ></v-list-item>
          </template>
          <v-list-item
            v-for="([title, icon, link], i) in admins"
            :key="i"
            :prepend-icon="icon"
            :title="title"
            :value="title"
            @click="navigateTo(link)"
            rounded="shaped"
            color="primary"
          ></v-list-item>
        </v-list-group>
        <!-- 4. Device Managemrnt -->
        <v-list-group value="Device" rounded="shaped" color="primary">
          <template v-slot:activator="{ props }">
            <v-list-item
              v-bind="props"
              prepend-icon="mdi-devices"
              title="Device"
              rounded="shaped"
              color="primary"
            ></v-list-item>
          </template>

          <v-list-item
            v-for="([title, icon, link], i) in device"
            :key="i"
            :prepend-icon="icon"
            :title="title"
            :value="title"
            @click="navigateTo(link)"
            rounded="shaped"
            color="primary"
          ></v-list-item>
        </v-list-group>
        <!-- 5. Setting -->
        <v-list-group value="Setting" rounded="shaped" color="primary">
          <template v-slot:activator="{ props }">
            <v-list-item
              v-bind="props"
              prepend-icon="mdi-cog"
              title="Setting"
              rounded="shaped"
              color="primary"
            ></v-list-item>
          </template>

          <v-list-item
            v-for="([title, icon, link], i) in setting"
            :key="i"
            :prepend-icon="icon"
            :title="title"
            :value="title"
            @click="navigateTo(link)"
            rounded="shaped"
            color="primary"
          ></v-list-item>
        </v-list-group>
      </v-list>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar color="primary" density="compact" :elevation="0">
      <v-app-bar-nav-icon @click="drawer = !drawer"></v-app-bar-nav-icon>
      <!-- Title Bar -->
      <v-app-bar-title>Application</v-app-bar-title>

      <!-- Search -->
      <v-text-field
        hide-details
        single-line
        :loading="loading"
        append-inner-icon="mdi-magnify"
        density="compact"
        label="Search templates"
        variant="solo"
        @click:append-inner="onClick"
        width="20rem"
      ></v-text-field>

      <!-- Notification Icon -->
      <v-menu ml-3>
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" icon="mdi-bell-badge"> </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="(noti, index) in notis"
            :key="index"
            :value="index"
          >
            <v-list-item-title>{{ noti.title }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
      <v-menu ml-3>
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" icon="mdi-cog"> </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="(item, index) in items"
            :key="index"
            :value="index"
          >
            <v-list-item-title>{{ item.title }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
      <!-- Icon Logout -->
      <v-btn icon="mdi-logout"></v-btn>
    </v-app-bar>

    <v-main>
      <!--  -->
      <slot />
    </v-main>

    <v-footer class="bg-primary text-center d-flex flex-column">
      <div>
        <v-btn
          v-for="icon in icons"
          :key="icon"
          :icon="icon"
          class="mx-4"
          variant="text"
        ></v-btn>
      </div>

      <div class="pt-0">
        Phasellus feugiat arcu sapien, et iaculis ipsum elementum sit amet.

      </div>

      <v-divider></v-divider>

      <div>{{ new Date().getFullYear() }} — <strong>Vuetify</strong></div>
    </v-footer>
  </v-app>
  
</template>


<script setup>
import { ref } from "vue";

const drawer = ref(null);
</script>

<script>
export default {
  data: () => ({
    drawer: null,
    items: [
      { title: "Profiles", icon: "mdi-account" },
      { title: "Notification", icon: "mdi-bell" },
      { title: "Change Password", icon: "mdi-lock" },
    ],
    notis: [
      { title: "Notification 1", icon: "mdi-bell" },
      { title: "Notification 2", icon: "mdi-bell" },
      { title: "Notification 3", icon: "mdi-bell" },
      { title: "Notification 4", icon: "mdi-bell" },
    ],
    dash: [["Dashboard", "mdi-monitor-dashboard", "/dashboards"]],
    open: ["Account"],
    admins: [
      ["Users", "mdi-account-multiple-outline", "/accounts/"],
      ["Gruops", "mdi-account-group", "/accounts/group"],
      ["Roles", "mdi-security", "/accounts/role"],
    ],
    open: ["Device"],
    device: [
      ["Computers", "mdi-laptop", "/devices/computer"],
      ["Devices", "mdi-toolbox", "/devices/"],
    ],
    open: ["Setting"],
    setting: [
      ["Organization", "mdi-home-city-outline", "/settings/"],
      ["Province", "mdi mdi-city", "/settings/organizations/province/"],
      ["District", "mdi-home", "/settings/organizations/district"],
      [
        "Location",
        "mdi-office-building-marker-outline",
        "/settings/organizations/location",
      ],
      ["Department", "mdi-toolbox", "/settings/organizations/department"],
      ["Notifacation", "mdi-bell-cog", "/settings/notification"],
      ["Systems logs", "mdi-semantic-web", "/settings"],
    ],
    icons: ["mdi-facebook", "mdi-twitter", "mdi-linkedin", "mdi-instagram"],
  }),
};
</script>
