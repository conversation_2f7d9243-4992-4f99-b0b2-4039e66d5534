<template>
  <div class="pa-4">
    <!-- Search -->
    <v-card color="grey-lighten-5">
      <v-container>
        <span>Search</span>
        <v-row no-gutters style="height: 50px">
          <v-col sm="3">
            <v-autocomplete
              v-model="selectedHeaders"
              :items="headers"
              label="Favorite headers"
              multiple
              variant="solo"
              density="compact"
              class="pa-2 normal-size-text-field"
            >
              <template v-slot:prepend-item>
                <v-list-item title="Select All" @click="toggle">
                  <template v-slot:prepend>
                    <v-checkbox-btn
                      :color="likesSomeHeader ? 'indigo-darken-4' : undefined"
                      :indeterminate="likesSomeHeader && !likesAllHeader"
                      :model-value="likesAllHeader"
                    ></v-checkbox-btn>
                  </template>
                </v-list-item>

                <v-divider class="mt-2"></v-divider>
              </template>
            </v-autocomplete>
          </v-col>
          <v-col sm="9">
            <v-text-field
              label="Search"
              prepend-inner-icon="mdi-magnify"
              variant="solo"
              density="compact"
              class="pa-2 normal-size-text-field"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-responsive width="100%"></v-responsive>
        <v-row class="text-right">
          <v-col sm="2" class="pa-5 ml-auto">
            <v-btn prepend-icon="mdi-magnify"> Search </v-btn>
          </v-col>
        </v-row>
      </v-container>
    </v-card>

    <!-- Add User -->
    <v-row class="text-right">
      <v-col sm="2" class="pa-5 ml-auto">
        <v-btn
          color="primary"
          prepend-icon="mdi-account-circle"
          @click="handleDialogInsert"
        >
          Add
        </v-btn>
      </v-col>
    </v-row>

    <!-- Table -->
    <v-card color="grey-lighten-5 mt-3">
      <v-data-table
        :headers="headers"
        :items="desserts"
        :loading="loadingTable"
        :sort-by="[
          { key: 'Account', order: 'asc' },
          { key: 'Role', order: 'desc' },
        ]"
        multi-sort
      >
        <template #[`item.actions`]="{ item }">
          <v-icon color="#F1A22E" @click="handleDialogUpdate(item)">
            mdi-calendar-edit
          </v-icon>
          <v-icon color="#F1A22E" @click="processDelete(item)">
            mdi-delete
          </v-icon>
        </template>
      </v-data-table>
    </v-card>
  </div>
</template>

<script setup>
//Mete Header
useHead({
  title: "Account",
  mete: [{ name: "description", content: "รายละเอียดอธิบายหัวข้อ" }],
});
//Mete Header
</script>

<script>
import axios from 'axios';

export default {
  data: () => ({
    loadingTable: false,
    selectedHeaders: [],
    // Data Table
    headers: [
      {
        title: "Name",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "name",
      },
      {
        title: "Account",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "account",
        key: "account"
      },
      {
        title: "Position",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "position",
      },
      {
        title: "Group",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "group",
      },
      {
        title: "Role",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "role",
      },
      {
        title: "Status",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "status",
      },
      {
        title: "Create Date",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "createDate",
      },
      {
        title: "Actions",
        align: "start",
        class: "grey lighten-3",
        sortable: true,
        value: "actions",
      },
    ],
    desserts: [],
  }),

  // Search Module
  computed: {
    likesAllHeader() {
      return this.selectedHeaders.length === this.headers.length;
    },
    likesSomeHeader() {
      return this.selectedHeaders.length > 0;
    },
    title() {
      if (this.likesAllHeader)
        return "Holy smokes, someone call the Header police!";

      if (this.likesSomeHeader) return "Header Count";

      return "How could you not like Header?";
    },
    subtitle() {
      if (this.likesAllHeader) return undefined;

      if (this.likesSomeHeader) return this.selectedHeaders.length;

      return "Go ahead, make a selection above!";
    },
  },
  // Search Module
  methods: {
    toggle() {
      if (this.likesAllHeader) {
        this.selectedHeaders = [];
      } else {
        this.selectedHeaders = this.headers.slice();
      }
    },
    async fetchData() {
      try {
        this.loadingTable = true;
        const response = await axios.get('http://localhost/templeted-nuxt3/api/users');
        this.desserts = response.data;
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        this.loadingTable = false;
      }
    },
    handleDialogInsert() {
      // Logic for handling insert dialog
    },
    handleDialogUpdate(item) {
      // Logic for handling update dialog
    },
    processDelete(item) {
      // Logic for deleting item
    },
    toggle() {
      if (this.likesAllHeader) {
        this.selectedHeaders = [];
      } else {
        this.selectedHeaders = [...this.headers];
      }
    },
  },
  
};
</script>

<style>
.normal-size-text-field .v-input__control {
  height: auto !important;
}
</style>
